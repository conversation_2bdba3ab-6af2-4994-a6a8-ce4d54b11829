const { MongoClient } = require('mongodb');

async function fixGitLabAccount() {
  const client = new MongoClient('mongodb://localhost:27017');
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db('test');
    const accountsCollection = db.collection('accounts');
    
    // Find GitLab accounts without userId
    const brokenAccounts = await accountsCollection.find({
      provider: 'gitlab',
      $or: [
        { userId: { $exists: false } },
        { userId: null },
        { userId: undefined }
      ]
    }).toArray();
    
    console.log(`Found ${brokenAccounts.length} broken GitLab accounts:`, brokenAccounts);
    
    if (brokenAccounts.length > 0) {
      const userId = '68b168037da2d89306a9316e';
      
      for (const account of brokenAccounts) {
        console.log(`Fixing account ${account._id} - setting userId to ${userId}`);
        
        const result = await accountsCollection.updateOne(
          { _id: account._id },
          { $set: { userId: userId } }
        );
        
        console.log(`Update result:`, result);
      }
      
      console.log(`✅ Fixed ${brokenAccounts.length} GitLab accounts`);
      
      // Verify the fix
      const fixedAccounts = await accountsCollection.find({
        provider: 'gitlab',
        userId: userId
      }).toArray();
      
      console.log(`✅ Verification: Found ${fixedAccounts.length} GitLab accounts with correct userId`);
    } else {
      console.log('No broken GitLab accounts found');
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

fixGitLabAccount();
