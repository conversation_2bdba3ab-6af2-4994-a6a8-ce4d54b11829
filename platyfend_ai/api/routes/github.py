import time
import uuid
from typing import Any, Dict, <PERSON><PERSON>, Tu<PERSON>

from fastapi import APIRout<PERSON>, Body, Depends, Header, HTTPException, Request, status

from platyfend_ai.dependencies import (
    check_service_health,
    get_analysis_services,
    get_pr_analysis_service,
)
from platyfend_ai.models.github import GitHubPRWebhookData
from platyfend_ai.utils.logging_config import get_logger, set_request_context
from platyfend_ai.utils.security_service import SecurityService
from platyfend_ai.utils.webhook_security import validate_github_webhook

logger = get_logger(__name__)

github_routes = APIRouter(
    tags=["GitHub"], dependencies=[Depends(SecurityService.get_api_key)]
)


@github_routes.post("/pr_open", response_model=Dict[str, Any])
async def github_pr_open(
    request: Request,
    pr_data: GitHubPRWebhookData = Body(..., description="GitHub PR webhook data"),
    x_github_delivery: Optional[str] = Header(
        None,
        alias="X-GitHub-Delivery",
        description="GitHub webhook delivery ID for idempotency",
    ),
    webhook_validated: bool = Depends(validate_github_webhook),
    analysis_services=Depends(get_analysis_services),
    pr_analysis_service=Depends(get_pr_analysis_service),
) -> Dict[str, Any]:
    """
    Handle GitHub Pull Request webhook events with security validation and idempotency support.

    This endpoint receives GitHub PR webhook data when a PR is opened, updated, or closed.
    It validates the webhook signature using HMAC-SHA256, processes the PR data, and triggers
    the appropriate analysis workflows. Uses X-GitHub-Delivery header for idempotency to
    prevent duplicate processing.

    Security Features:
    - HMAC-SHA256 signature validation using X-Hub-Signature-256 header
    - Idempotency protection using X-GitHub-Delivery header
    - API key authentication via SecurityService dependency

    Args:
        request: FastAPI Request object (used for signature validation)
        pr_data: GitHub PR webhook data containing all relevant PR information
        x_github_delivery: GitHub delivery ID for idempotency (from X-GitHub-Delivery header)
        webhook_validated: Dependency that validates webhook signature (automatically injected)
        analysis_services: Injected analysis services
        pr_analysis_service: Injected PR analysis service

    Returns:
        Dict containing the processing status and any relevant information

    Raises:
        HTTPException: If processing fails, signature validation fails, or other errors occur
    """
    try:
        # Generate unique request ID for tracking
        request_id = str(uuid.uuid4())[:8]

        # Set structured logging context
        set_request_context(
            request_id=request_id, operation="pr_webhook", user_id=pr_data.author
        )

        logger.info(
            "Received PR webhook",
            repository=pr_data.repository,
            pr_number=pr_data.number,
            pr_title=pr_data.title,
            action=pr_data.action,
            state=pr_data.state,
            author=pr_data.author,
            sender=pr_data.sender,
            delivery_id=x_github_delivery,
        )

        # Log delivery ID for tracking
        if x_github_delivery:
            logger.info("GitHub delivery ID received", delivery_id=x_github_delivery)
        else:
            logger.warning(
                "No X-GitHub-Delivery header found - idempotency not guaranteed"
            )

        # Check idempotency using GitHub delivery ID
        # if x_github_delivery:
        #     is_already_processed = (
        #         await pr_analysis_service.idempotency_service.is_delivery_processed(
        #             x_github_delivery
        #         )
        #     )
        #     if is_already_processed:
        #         logger.info(
        #             f"[{request_id}] Webhook delivery {x_github_delivery} already processed - returning cached response"
        #         )
        #         return {
        #             "status": "skipped",
        #             "reason": f"Webhook delivery {x_github_delivery} already processed",
        #             "pr_number": pr_data.number,
        #             "repository": pr_data.repository,
        #             "delivery_id": x_github_delivery,
        #         }

        # Log key PR metrics
        logger.info(
            f"[{request_id}] PR stats - Commits: {pr_data.commits}, "
            f"Files changed: {pr_data.changed_files}, "
            f"Additions: {pr_data.additions}, "
            f"Deletions: {pr_data.deletions}"
        )

        # Delegate to the PR analysis service
        response_data = await pr_analysis_service.process_pr_webhook(
            pr_data, analysis_services, request_id, x_github_delivery
        )

        return response_data

    except Exception as e:
        logger.error(f"Error processing PR webhook: {str(e)}", exc_info=True)

        # Mark delivery as failed if we have a delivery ID
        if x_github_delivery:
            try:
                await pr_analysis_service.idempotency_service.mark_delivery_processed(
                    x_github_delivery, pr_data, "error"
                )
            except Exception as mark_error:
                logger.error(
                    f"Failed to mark delivery {x_github_delivery} as error: {mark_error}"
                )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process PR webhook: {str(e)}",
        )


@github_routes.get("/health", response_model=Dict[str, Any])
async def health_check() -> Dict[str, Any]:
    """
    Health check endpoint that verifies all services are properly configured.

    Returns:
        Dict containing health status of all services
    """
    try:
        health_status = await check_service_health()

        # Determine overall health
        unhealthy_services = [
            service
            for service, status in health_status.items()
            if status in ["unhealthy", "not_configured", "error"]
        ]

        overall_status = "healthy" if not unhealthy_services else "degraded"

        return {
            "status": overall_status,
            "services": health_status,
            "unhealthy_services": unhealthy_services,
            "timestamp": time.time(),
        }

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {"status": "error", "error": str(e), "timestamp": time.time()}
