"""Comment processing service for generating and sending security review comments."""

import time
from typing import Any, Dict, List

import httpx

from platyfend_ai.config.settings import settings
from platyfend_ai.models.github import GitHubPRWebhookData
from platyfend_ai.services import CommentGenerationError, SecurityCommentGenerator
from platyfend_ai.utils.logging_config import get_logger

logger = get_logger(__name__)


class CommentProcessingService:
    """Service for processing and sending security review comments."""

    def __init__(self, comment_generator: SecurityCommentGenerator):
        self.comment_generator = comment_generator

    def _extract_relative_path_from_temp_path(self, file_path: str) -> str:
        """
        Extract the relative path from repository root, handling temporary directory paths.

        Args:
            file_path: Full file path (may include /tmp/pr_xxx/ prefix)

        Returns:
            Relative path from repository root (e.g., "src/main.py" instead of "/tmp/pr_5_x3zvj_9r/src/main.py")
        """
        if not file_path:
            return ""

        # If path contains /tmp/pr_xxx pattern, extract everything after the temp directory
        if "/tmp/pr_" in file_path:
            # Find the pattern /tmp/pr_xxx_xxx/ and extract what comes after
            parts = file_path.split("/")
            temp_dir_found = False
            result_parts = []

            for part in parts:
                if temp_dir_found:
                    result_parts.append(part)
                elif part.startswith("pr_") and "_" in part:
                    temp_dir_found = True

            if result_parts:
                return "/".join(result_parts)

        # If no temp pattern found, assume it's already a relative path
        return file_path

    async def generate_review_comments(
        self,
        analysis_report,
        pr_data: GitHubPRWebhookData,
        request_id: str,
    ) -> List[Any]:
        """
        Generate review comments based on analysis results.

        Args:
            analysis_report: Analysis report from security analysis
            pr_data: GitHub PR webhook data
            request_id: Request ID for logging

        Returns:
            List of review comments
        """
        logger.info(f"[{request_id}] Step 4/5: Generating security review comments")

        if analysis_report.total_findings > 0:
            try:
                # Collect all findings from analysis results
                all_findings = []
                for result in analysis_report.analysis_results:
                    all_findings.extend(result.findings)

                # Generate review comments using injected service
                review_comments = await self.comment_generator.generate_review_comments(
                    all_findings,
                    pr_context={
                        "title": pr_data.title,
                        "repository": pr_data.repository,
                        "author": pr_data.author,
                        "number": pr_data.number,
                    },
                )

                logger.info(
                    f"[{request_id}] Step 4/5 completed: Generated {len(review_comments)} review comments for {len(all_findings)} findings"
                )
                return review_comments

            except CommentGenerationError as e:
                logger.error(f"[{request_id}] Comment generation failed: {e}")
                return []  # Continue with empty comments
        else:
            try:
                # Generate a positive security message when no issues are found
                review_comments = (
                    await self.comment_generator.generate_no_findings_message(
                        pr_context={
                            "title": pr_data.title,
                            "repository": pr_data.repository,
                            "author": pr_data.author,
                            "number": pr_data.number,
                        },
                    )
                )

                logger.info(
                    f"[{request_id}] Step 4/5 completed: Generated positive security message for clean PR"
                )
                return review_comments

            except CommentGenerationError as e:
                logger.error(
                    f"[{request_id}] Failed to generate no-findings message: {e}"
                )
                return []

    async def send_comments_to_frontend(
        self, pr_data: GitHubPRWebhookData, comments: List[Any], analysis_report
    ) -> Dict[str, Any]:
        """
        Send security review comments to Next.js frontend instead of GitHub.

        Args:
            pr_data: GitHub PR webhook data
            comments: List of review comments
            analysis_report: Analysis results

        Returns:
            Dict with results of sending comments
        """
        frontend_url = getattr(settings, "frontend_url", "http://localhost:3001")
        # Validate URL format
        from urllib.parse import urlparse

        try:
            parsed = urlparse(frontend_url)
            if not parsed.scheme or not parsed.netloc:
                raise ValueError(f"Invalid frontend URL format: {frontend_url}")
        except Exception as e:
            logger.error(f"Invalid frontend URL configuration: {e}")
            return {"sent_comments": 0, "errors": [f"Invalid frontend URL: {e}"]}

        endpoint = f"{frontend_url}/api/security-review"

        # Prepare payload for frontend
        payload = {
            "pr_info": {
                "id": pr_data.id,
                "number": pr_data.number,
                "title": pr_data.title,
                "repository": pr_data.repository,
                "author": pr_data.author,
                "url": pr_data.url,
                "base_branch": pr_data.base_branch,
                "head_branch": pr_data.head_branch,
                "action": pr_data.action,
            },
            "analysis_summary": {
                "total_findings": analysis_report.total_findings,
                "critical_findings": analysis_report.critical_findings,
                "high_findings": analysis_report.high_findings,
                "success": analysis_report.success,
                "analyzers_run": len(analysis_report.analysis_results),
            },
            "comments": [
                {
                    "body": comment.body,
                    "file_path": self._extract_relative_path_from_temp_path(comment.file_path or ""),
                    "line": comment.line or 0,
                    "severity": comment.severity.value if comment.severity else "",
                    "comment_type": comment.comment_type,
                    "finding_ids": [str(finding_id) for finding_id in comment.finding_ids],
                }
                for comment in comments
            ],
            "timestamp": time.time(),
        }

        try:
            # Get API key for frontend authentication from settings
            api_key = getattr(settings, "api_key", None)

            headers = {"Content-Type": "application/json"}
            if api_key:
                headers["x-api-key"] = api_key

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(endpoint, json=payload, headers=headers)

                if response.status_code == 200:
                    logger.info(
                        f"Successfully sent {len(comments)} comments to frontend"
                    )
                    return {
                        "sent_comments": len(comments),
                        "errors": [],
                        "frontend_response": (
                            response.json() if response.content else None
                        ),
                    }
                else:
                    error_msg = f"Frontend returned status {response.status_code}: {response.text}"
                    logger.error(error_msg)
                    return {"sent_comments": 0, "errors": [error_msg]}

        except httpx.RequestError as e:
            error_msg = f"Failed to connect to frontend: {e}"
            logger.error(error_msg)
            return {"sent_comments": 0, "errors": [error_msg]}
        except Exception as e:
            error_msg = f"Unexpected error sending to frontend: {e}"
            logger.error(error_msg)
            return {"sent_comments": 0, "errors": [error_msg]}
