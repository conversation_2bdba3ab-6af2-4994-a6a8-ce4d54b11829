import asyncio
import json
import logging
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional

from platyfend_ai.config.settings import settings
from platyfend_ai.core.analysis_engine import Ana<PERSON>zerError, BaseAnalyzer
from platyfend_ai.models.analysis import (
    AnalysisResult,
    AnalyzerType,
    CodeLocation,
    SecurityFinding,
    SeverityLevel,
)

logger = logging.getLogger(__name__)


class LinterAnalyzer(BaseAnalyzer):
    """Multi-language linter analyzer implementation"""

    def __init__(self, enabled_linters: Optional[List[str]] = None):
        """
        Initialize the Linter analyzer.

        Args:
            enabled_linters: List of linters to enable (e.g., ['ruff', 'eslint', 'pylint'])
        """
        super().__init__("Linter", AnalyzerType.LINTER, "multi-linter")

        self.enabled_linters = enabled_linters or ["ruff", "eslint", "pylint", "flake8"]
        self.timeout = getattr(settings, "linter_timeout", 120)

        # Configure available linters
        self.linter_configs = {
            "ruff": {
                "command": ["ruff", "check", "--output-format=json"],
                "extensions": [".py"],
                "check_cmd": ["ruff", "--version"],
            },
            "pylint": {
                "command": ["pylint", "--output-format=json"],
                "extensions": [".py"],
                "check_cmd": ["pylint", "--version"],
            },
            "flake8": {
                "command": ["flake8", "--format=json"],
                "extensions": [".py"],
                "check_cmd": ["flake8", "--version"],
            },
            "eslint": {
                "command": ["eslint", "--format=json"],
                "extensions": [".js", ".jsx", ".ts", ".tsx"],
                "check_cmd": ["eslint", "--version"],
            },
            "tslint": {
                "command": ["tslint", "--format=json"],
                "extensions": [".ts", ".tsx"],
                "check_cmd": ["tslint", "--version"],
            },
            "rubocop": {
                "command": ["rubocop", "--format=json"],
                "extensions": [".rb"],
                "check_cmd": ["rubocop", "--version"],
            },
            "golangci-lint": {
                "command": ["golangci-lint", "run", "--out-format=json"],
                "extensions": [".go"],
                "check_cmd": ["golangci-lint", "--version"],
            },
        }

        # Check which linters are available
        self.available_linters = self._check_available_linters()

        self.logger.info(
            f"Linter analyzer initialized with {len(self.available_linters)} available linters: {list(self.available_linters.keys())}"
        )

    def _check_available_linters(self) -> Dict[str, Dict[str, Any]]:
        """Check which linters are available on the system"""
        available = {}

        for linter_name in self.enabled_linters:
            if linter_name in self.linter_configs:
                config = self.linter_configs[linter_name]
                if self._is_linter_available(config["check_cmd"]):
                    available[linter_name] = config
                    self.logger.debug(f"Linter {linter_name} is available")
                else:
                    self.logger.debug(f"Linter {linter_name} is not available")

        return available

    def _is_linter_available(self, check_cmd: List[str]) -> bool:
        """Check if a specific linter is available"""
        try:
            import subprocess

            result = subprocess.run(
                check_cmd, capture_output=True, text=True, timeout=10
            )
            return result.returncode == 0
        except Exception:
            return False

    def is_available(self) -> bool:
        """Check if any linters are available"""
        return len(self.available_linters) > 0

    def get_supported_file_extensions(self) -> List[str]:
        """Get file extensions supported by available linters"""
        extensions = set()
        for config in self.available_linters.values():
            extensions.update(config["extensions"])
        return list(extensions)

    async def analyze(
        self, files_path: list[str], temp_dir: Path
    ) -> AnalysisResult:
        """
        Run linter analysis on the provided files.

        Args:
            files_path: File paths to analyze
            temp_dir: Temporary directory for analysis

        Returns:
            Analysis result with linting findings
        """
        started_at = datetime.now(timezone.utc)

        try:
            self.logger.info("Starting linter analysis")

            if not files_path:
                self.logger.info("No files provided for linter analysis")
                return AnalysisResult(
                    analyzer_type=self.analyzer_type,
                    analyzer_version=self.version,
                    started_at=started_at,
                    completed_at=datetime.now(timezone.utc),
                    duration_seconds=0,
                    success=True,
                    total_findings=0,
                    files_analyzed=[],
                    error_message=None,
                    raw_output="",
                )

            # Run linters on appropriate files
            all_findings = []
            all_analyzed_files = []

            for linter_name, config in self.available_linters.items():
                # Filter files for this linter
                linter_files = [
                    f
                    for f in files_path
                    if self._should_lint_file(f, config["extensions"])
                ]

                if linter_files:
                    self.logger.debug(
                        f"Running {linter_name} on {len(linter_files)} files"
                    )
                    linter_output = await self._run_linter(
                        linter_name, config, temp_dir, linter_files
                    )
                    findings = self._parse_linter_output(
                        linter_name, linter_output, temp_dir
                    )
                    all_findings.extend(findings)
                    all_analyzed_files.extend(linter_files)

            completed_at = datetime.now(timezone.utc)
            duration = (completed_at - started_at).total_seconds()

            result = AnalysisResult(
                analyzer_type=self.analyzer_type,
                analyzer_version=self.version,
                started_at=started_at,
                completed_at=completed_at,
                duration_seconds=duration,
                findings=all_findings,
                files_analyzed=list(set(all_analyzed_files)),
                success=True,
                total_findings=len(all_findings),
                error_message=None,
                raw_output="",
            )

            self.logger.info(
                f"Linter analysis completed: {len(all_findings)} findings in {duration:.2f}s"
            )
            return result

        except Exception as e:
            completed_at = datetime.now(timezone.utc)
            duration = (completed_at - started_at).total_seconds()

            self.logger.error(f"Linter analysis failed: {e}")

            return AnalysisResult(
                analyzer_type=self.analyzer_type,
                analyzer_version=self.version,
                started_at=started_at,
                completed_at=completed_at,
                duration_seconds=duration,
                success=False,
                error_message=str(e),
                total_findings=0,
                files_analyzed=[],
                raw_output="",
            )

    async def _extract_changed_files(
        self, _diff_content: str, files_info: Dict[str, Any], temp_dir: Path
    ) -> List[str]:
        """
        Extract changed files from diff and write them to temp directory.

        Args:
            diff_content: The diff content
            files_info: File information from GitHub API
            temp_dir: Temporary directory to write files

        Returns:
            List of file paths that were extracted
        """
        changed_files = []

        # Get list of changed files from files_info
        files = files_info.get("files", [])

        for file_info in files:
            filename = file_info.get("filename", "")
            status = file_info.get("status", "")

            # Skip deleted files
            if status == "removed":
                continue

            # Check if file is supported by any linter
            if not self.should_analyze_file(filename):
                continue

            # For now, we'll create placeholder files since we only have the diff
            # In a real implementation, you'd fetch the full file content
            file_path = temp_dir / filename
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # Create a placeholder file with some content
            # This is a simplified approach - in production you'd want to reconstruct
            # the actual file content from the diff or fetch it from the repository
            file_path.write_text(
                f"# Placeholder for {filename}\n# Analysis based on diff content\n"
            )

            changed_files.append(str(file_path.relative_to(temp_dir)))

        return changed_files

    def _should_lint_file(self, file_path: str, extensions: List[str]) -> bool:
        """Check if a file should be linted based on its extension"""
        file_ext = Path(file_path).suffix.lower()
        return file_ext in extensions

    async def _run_linter(
        self, linter_name: str, config: Dict[str, Any], temp_dir: Path, files: List[str]
    ) -> str:
        """
        Run a specific linter on the given files.

        Args:
            linter_name: Name of the linter
            config: Linter configuration
            temp_dir: Directory containing files
            files: List of files to lint

        Returns:
            Linter output
        """
        cmd = config["command"].copy()

        # Add files to command
        if linter_name == "ruff":
            # Ruff can take multiple files
            cmd.extend(files)
        elif linter_name in ["eslint", "tslint"]:
            # ESLint and TSLint can take multiple files
            cmd.extend(files)
        elif linter_name == "pylint":
            # Pylint can take multiple files
            cmd.extend(files)
        elif linter_name == "flake8":
            # Flake8 can take multiple files
            cmd.extend(files)
        else:
            # For other linters, add files
            cmd.extend(files)

        self.logger.debug(f"Running {linter_name} command: {' '.join(cmd)}")

        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=temp_dir,
            )

            stdout, stderr = await asyncio.wait_for(
                process.communicate(), timeout=self.timeout
            )

            # Most linters return non-zero when they find issues, so don't fail on that
            output = stdout.decode("utf-8")

            if not output and stderr:
                # If no stdout but there's stderr, it might be an error
                error_msg = stderr.decode("utf-8")
                if "error" in error_msg.lower() or "failed" in error_msg.lower():
                    self.logger.warning(f"{linter_name} reported error: {error_msg}")

            return output

        except asyncio.TimeoutError:
            raise AnalyzerError(
                f"{linter_name} analysis timed out after {self.timeout} seconds"
            )
        except Exception as e:
            raise AnalyzerError(f"Failed to run {linter_name}: {e}")

    def _parse_linter_output(
        self, linter_name: str, output: str, temp_dir: Path
    ) -> List[SecurityFinding]:
        """
        Parse linter output into SecurityFinding objects.

        Args:
            linter_name: Name of the linter
            output: Linter output
            temp_dir: Temporary directory used for analysis

        Returns:
            List of security findings
        """
        if not output.strip():
            return []

        try:
            if linter_name == "ruff":
                return self._parse_ruff_output(output, temp_dir)
            elif linter_name == "eslint":
                return self._parse_eslint_output(output, temp_dir)
            elif linter_name == "pylint":
                return self._parse_pylint_output(output, temp_dir)
            elif linter_name == "flake8":
                return self._parse_flake8_output(output, temp_dir)
            else:
                # Generic JSON parser
                return self._parse_generic_json_output(linter_name, output, temp_dir)

        except Exception as e:
            self.logger.error(f"Failed to parse {linter_name} output: {e}")
            return []

    def _parse_ruff_output(self, output: str, temp_dir: Path) -> List[SecurityFinding]:
        """Parse Ruff JSON output"""
        findings = []

        try:
            data = json.loads(output)
            for item in data:
                finding = self._create_finding_from_ruff_item(item, temp_dir)
                if finding:
                    findings.append(finding)
        except json.JSONDecodeError:
            self.logger.warning("Failed to parse Ruff output as JSON")

        return findings

    def _create_finding_from_ruff_item(
        self, item: Dict[str, Any], temp_dir: Path
    ) -> Optional[SecurityFinding]:
        """Create SecurityFinding from Ruff item"""
        try:
            filename = item.get("filename", "")
            location_info = item.get("location", {})

            # Make path relative
            try:
                relative_path = Path(filename).relative_to(temp_dir)
                file_path = str(relative_path)
            except ValueError:
                file_path = filename

            location = CodeLocation(
                file_path=file_path,
                line_start=location_info.get("row", 1),
                column_start=location_info.get("column", 1),
                line_end=location_info.get("end_row"),
                column_end=location_info.get("end_column"),
            )

            # Map Ruff severity
            severity = self._map_linter_severity("ruff", item.get("code", ""))

            finding = SecurityFinding(
                analyzer_type=self.analyzer_type,
                rule_id=item.get("code", "unknown"),
                rule_name=item.get("code", "Unknown Rule"),
                severity=severity,
                location=location,
                title=f"Ruff: {item.get('code', 'Unknown')}",
                description=item.get("message", ""),
                message=item.get("message", ""),
                metadata={"linter": "ruff", "original": item},
                code_snippet=None,
                fix_suggestion=None,
                confidence=None,
            )

            return finding

        except Exception as e:
            self.logger.warning(f"Failed to parse Ruff item: {e}")
            return None

    def _parse_eslint_output(
        self, output: str, temp_dir: Path
    ) -> List[SecurityFinding]:
        """Parse ESLint JSON output"""
        findings = []

        try:
            data = json.loads(output)
            for file_result in data:
                for message in file_result.get("messages", []):
                    finding = self._create_finding_from_eslint_message(
                        file_result, message, temp_dir
                    )
                    if finding:
                        findings.append(finding)
        except json.JSONDecodeError:
            self.logger.warning("Failed to parse ESLint output as JSON")

        return findings

    def _create_finding_from_eslint_message(
        self, file_result: Dict[str, Any], message: Dict[str, Any], temp_dir: Path
    ) -> Optional[SecurityFinding]:
        """Create SecurityFinding from ESLint message"""
        try:
            filename = file_result.get("filePath", "")

            # Make path relative
            try:
                relative_path = Path(filename).relative_to(temp_dir)
                file_path = str(relative_path)
            except ValueError:
                file_path = filename

            location = CodeLocation(
                file_path=file_path,
                line_start=message.get("line", 1),
                column_start=message.get("column", 1),
                line_end=message.get("endLine"),
                column_end=message.get("endColumn"),
            )

            # Map ESLint severity
            severity = self._map_eslint_severity(message.get("severity", 1))

            finding = SecurityFinding(
                analyzer_type=self.analyzer_type,
                rule_id=message.get("ruleId", "unknown"),
                rule_name=message.get("ruleId", "Unknown Rule"),
                severity=severity,
                location=location,
                title=f"ESLint: {message.get('ruleId', 'Unknown')}",
                description=message.get("message", ""),
                message=message.get("message", ""),
                metadata={"linter": "eslint", "original": message},
                code_snippet=None,
                fix_suggestion=None,
                confidence=None,
            )

            return finding

        except Exception as e:
            self.logger.warning(f"Failed to parse ESLint message: {e}")
            return None

    def _parse_pylint_output(
        self, output: str, temp_dir: Path
    ) -> List[SecurityFinding]:
        """Parse Pylint JSON output"""
        findings = []

        try:
            data = json.loads(output)
            for item in data:
                finding = self._create_finding_from_pylint_item(item, temp_dir)
                if finding:
                    findings.append(finding)
        except json.JSONDecodeError:
            self.logger.warning("Failed to parse Pylint output as JSON")

        return findings

    def _create_finding_from_pylint_item(
        self, item: Dict[str, Any], temp_dir: Path
    ) -> Optional[SecurityFinding]:
        """Create SecurityFinding from Pylint item"""
        try:
            filename = item.get("path", "")

            # Make path relative
            try:
                relative_path = Path(filename).relative_to(temp_dir)
                file_path = str(relative_path)
            except ValueError:
                file_path = filename

            location = CodeLocation(
                file_path=file_path,
                line_start=item.get("line", 1),
                column_start=item.get("column", 1),
                line_end=item.get("endLine"),
                column_end=item.get("endColumn"),
            )

            # Map Pylint severity
            severity = self._map_pylint_severity(item.get("type", "info"))

            finding = SecurityFinding(
                analyzer_type=self.analyzer_type,
                rule_id=item.get("message-id", "unknown"),
                rule_name=item.get("symbol", "Unknown Rule"),
                severity=severity,
                location=location,
                title=f"Pylint: {item.get('symbol', 'Unknown')}",
                description=item.get("message", ""),
                message=item.get("message", ""),
                metadata={"linter": "pylint", "original": item},
                code_snippet=None,
                fix_suggestion=None,
                confidence=None,
            )

            return finding

        except Exception as e:
            self.logger.warning(f"Failed to parse Pylint item: {e}")
            return None

    def _parse_flake8_output(
        self, output: str, temp_dir: Path
    ) -> List[SecurityFinding]:
        """Parse Flake8 output (usually not JSON, so parse line by line)"""
        findings = []

        for line in output.strip().split("\n"):
            if line.strip():
                finding = self._create_finding_from_flake8_line(line, temp_dir)
                if finding:
                    findings.append(finding)

        return findings

    def _create_finding_from_flake8_line(
        self, line: str, temp_dir: Path
    ) -> Optional[SecurityFinding]:
        """Create SecurityFinding from Flake8 line"""
        try:
            # Flake8 format: filename:line:column: code message
            parts = line.split(":", 3)
            if len(parts) < 4:
                return None

            filename = parts[0]
            line_num = int(parts[1])
            column = int(parts[2])
            code_and_message = parts[3].strip()

            # Extract code and message
            code_parts = code_and_message.split(" ", 1)
            code = code_parts[0] if code_parts else "unknown"
            message = code_parts[1] if len(code_parts) > 1 else ""

            # Make path relative
            try:
                relative_path = Path(filename).relative_to(temp_dir)
                file_path = str(relative_path)
            except ValueError:
                file_path = filename

            location = CodeLocation(
                file_path=file_path,
                line_start=line_num,
                column_start=column,
                line_end=None,
                column_end=None,
            )

            # Map Flake8 severity
            severity = self._map_linter_severity("flake8", code)

            finding = SecurityFinding(
                analyzer_type=self.analyzer_type,
                rule_id=code,
                rule_name=code,
                severity=severity,
                location=location,
                title=f"Flake8: {code}",
                description=message,
                message=message,
                metadata={"linter": "flake8", "original_line": line},
                code_snippet=None,
                fix_suggestion=None,
                confidence=None,
            )

            return finding

        except Exception as e:
            self.logger.warning(f"Failed to parse Flake8 line: {e}")
            return None

    def _parse_generic_json_output(
        self, linter_name: str, output: str, temp_dir: Path
    ) -> List[SecurityFinding]:
        """Generic JSON parser for unknown linters"""
        findings = []

        try:
            data = json.loads(output)
            if isinstance(data, list):
                for item in data:
                    finding = self._create_generic_finding(linter_name, item, temp_dir)
                    if finding:
                        findings.append(finding)
        except json.JSONDecodeError:
            self.logger.warning(f"Failed to parse {linter_name} output as JSON")

        return findings

    def _create_generic_finding(
        self, linter_name: str, item: Dict[str, Any], temp_dir: Path
    ) -> Optional[SecurityFinding]:
        """Create a generic SecurityFinding"""
        try:
            # Try to extract common fields
            filename = item.get(
                "file", item.get("filename", item.get("path", "unknown"))
            )
            line_num = item.get("line", item.get("lineNumber", 1))
            message = item.get("message", item.get("description", ""))
            rule_id = item.get("rule", item.get("ruleId", item.get("code", "unknown")))

            # Make path relative
            try:
                relative_path = Path(filename).relative_to(temp_dir)
                file_path = str(relative_path)
            except ValueError:
                file_path = filename

            location = CodeLocation(
                file_path=file_path,
                line_start=line_num,
                line_end=None,
                column_start=None,
                column_end=None,
            )

            finding = SecurityFinding(
                analyzer_type=self.analyzer_type,
                rule_id=rule_id,
                rule_name=rule_id,
                severity=SeverityLevel.INFO,
                location=location,
                title=f"{linter_name}: {rule_id}",
                description=message,
                message=message,
                metadata={"linter": linter_name, "original": item},
                code_snippet=None,
                fix_suggestion=None,
                confidence=None,
            )

            return finding

        except Exception as e:
            self.logger.warning(f"Failed to create generic finding: {e}")
            return None

    def _map_linter_severity(self, _linter_name: str, code: str) -> SeverityLevel:
        """Map linter-specific codes to severity levels"""
        # Security-related codes that should be high severity
        security_codes = {
            "S",
            "B",  # Bandit security codes
            "DUO",  # Duo security
            "SEC",  # Security
        }

        if any(code.startswith(sec_code) for sec_code in security_codes):
            return SeverityLevel.HIGH

        # Error codes
        if code.startswith("E"):
            return SeverityLevel.MEDIUM

        # Warning codes
        if code.startswith("W"):
            return SeverityLevel.LOW

        return SeverityLevel.INFO

    def _map_eslint_severity(self, severity: int) -> SeverityLevel:
        """Map ESLint severity to our levels"""
        if severity == 2:  # Error
            return SeverityLevel.MEDIUM
        elif severity == 1:  # Warning
            return SeverityLevel.LOW
        else:
            return SeverityLevel.INFO

    def _map_pylint_severity(self, pylint_type: str) -> SeverityLevel:
        """Map Pylint message type to our levels"""
        type_map = {
            "error": SeverityLevel.HIGH,
            "warning": SeverityLevel.MEDIUM,
            "refactor": SeverityLevel.LOW,
            "convention": SeverityLevel.INFO,
            "info": SeverityLevel.INFO,
        }

        return type_map.get(pylint_type.lower(), SeverityLevel.INFO)
