"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card";
import { Checkbox } from "@/src/components/ui/checkbox";
import { Input } from "@/src/components/ui/input";
import { Badge } from "@/src/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/src/components/ui/alert";
import { LoadingSpinner } from "@/src/components/dashboard/loading-spinner";
import { 
  ArrowLeft, 
  Search, 
  GitBranch, 
  Star, 
  GitFork, 
  Lock, 
  Globe, 
  AlertCircle,
  CheckCircle2,
  Plus
} from "lucide-react";
import { getProviderDisplayName } from "@/src/lib/utils/provider";

interface Repository {
  id: string;
  externalId: string;
  name: string;
  fullName: string;
  provider: 'github' | 'gitlab';
  private: boolean;
  language?: string;
  stars: number;
  forks: number;
  description?: string;
  url?: string;
  defaultBranch?: string;
  lastActivity: string;
}

interface AddRepositoriesPageProps {
  organizationId: string;
}

export function AddRepositoriesPage({ organizationId }: AddRepositoriesPageProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [repositories, setRepositories] = useState<Repository[]>([]);
  const [selectedRepos, setSelectedRepos] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isAdding, setIsAdding] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Fetch available repositories
  useEffect(() => {
    const fetchRepositories = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/organizations/${organizationId}/repositories`);
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch repositories');
        }

        setRepositories(data.repositories || []);
      } catch (err: any) {
        setError(err.message || 'Failed to load repositories');
      } finally {
        setIsLoading(false);
      }
    };

    if (organizationId && session?.user?.id) {
      fetchRepositories();
    }
  }, [organizationId, session?.user?.id]);

  // Filter repositories based on search query
  const filteredRepositories = repositories.filter(repo =>
    repo.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    repo.fullName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    repo.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Group repositories by provider
  const repositoriesByProvider = filteredRepositories.reduce((acc, repo) => {
    if (!acc[repo.provider]) {
      acc[repo.provider] = [];
    }
    acc[repo.provider].push(repo);
    return acc;
  }, {} as Record<string, Repository[]>);

  const handleRepoToggle = (repoId: string) => {
    const newSelected = new Set(selectedRepos);
    if (newSelected.has(repoId)) {
      newSelected.delete(repoId);
    } else {
      newSelected.add(repoId);
    }
    setSelectedRepos(newSelected);
  };

  const handleSelectAll = (provider: string) => {
    const providerRepos = repositoriesByProvider[provider] || [];
    const newSelected = new Set(selectedRepos);
    
    const allSelected = providerRepos.every(repo => selectedRepos.has(repo.id));
    
    if (allSelected) {
      // Deselect all from this provider
      providerRepos.forEach(repo => newSelected.delete(repo.id));
    } else {
      // Select all from this provider
      providerRepos.forEach(repo => newSelected.add(repo.id));
    }
    
    setSelectedRepos(newSelected);
  };

  const handleAddRepositories = async () => {
    if (selectedRepos.size === 0) {
      setError('Please select at least one repository to add');
      return;
    }

    setIsAdding(true);
    setError(null);

    try {
      const selectedRepositories = repositories.filter(repo => selectedRepos.has(repo.id));
      
      const response = await fetch(`/api/organizations/${organizationId}/repositories/add`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          repositories: selectedRepositories
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to add repositories');
      }

      setSuccess(`Successfully added ${selectedRepos.size} repository(ies)`);
      
      // Redirect back to repositories page after a short delay
      setTimeout(() => {
        router.push(`/dashboard/${organizationId}/repositories`);
      }, 2000);

    } catch (err: any) {
      setError(err.message || 'Failed to add repositories');
    } finally {
      setIsAdding(false);
    }
  };

  const getLanguageColor = (language?: string) => {
    const colors: Record<string, string> = {
      'JavaScript': 'bg-yellow-500',
      'TypeScript': 'bg-blue-500',
      'Python': 'bg-green-500',
      'Java': 'bg-orange-500',
      'Go': 'bg-cyan-500',
      'Rust': 'bg-orange-600',
      'C++': 'bg-pink-500',
      'C#': 'bg-purple-500',
      'PHP': 'bg-indigo-500',
      'Ruby': 'bg-red-500',
    };
    return colors[language || ''] || 'bg-gray-500';
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-semibold text-slate-900">Add Repositories</h1>
            <p className="text-slate-600 mt-1">Loading available repositories...</p>
          </div>
        </div>
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-semibold text-slate-900">Add Repositories</h1>
            <p className="text-slate-600 mt-1">
              Select repositories to add to your workspace
            </p>
          </div>
        </div>
        
        {selectedRepos.size > 0 && (
          <Button 
            onClick={handleAddRepositories}
            disabled={isAdding}
            className="bg-primary hover:bg-primary/90"
          >
            {isAdding ? (
              <>
                <LoadingSpinner className="w-4 h-4 mr-2" />
                Adding...
              </>
            ) : (
              <>
                <Plus className="w-4 h-4 mr-2" />
                Add {selectedRepos.size} Repository(ies)
              </>
            )}
          </Button>
        )}
      </div>

      {/* Alerts */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertTitle className="text-green-800">Success</AlertTitle>
          <AlertDescription className="text-green-700">{success}</AlertDescription>
        </Alert>
      )}

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <Input
          placeholder="Search repositories..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Repository Lists by Provider */}
      {Object.keys(repositoriesByProvider).length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <GitBranch className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">
              No repositories found
            </h3>
            <p className="text-muted-foreground text-center mb-4 max-w-md">
              No repositories are available to add. Make sure you have connected your GitHub or GitLab account
              and have access to repositories.
            </p>
          </CardContent>
        </Card>
      ) : (
        Object.entries(repositoriesByProvider).map(([provider, repos]) => (
          <Card key={provider}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <span>{getProviderDisplayName(provider)} Repositories</span>
                    <Badge variant="secondary">{repos.length}</Badge>
                  </CardTitle>
                  <CardDescription>
                    Select repositories from your {getProviderDisplayName(provider)} account
                  </CardDescription>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSelectAll(provider)}
                >
                  {repos.every(repo => selectedRepos.has(repo.id)) ? 'Deselect All' : 'Select All'}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {repos.map((repo) => (
                  <div
                    key={repo.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      selectedRepos.has(repo.id)
                        ? 'border-primary bg-primary/5'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleRepoToggle(repo.id)}
                  >
                    <div className="flex items-start space-x-3">
                      <Checkbox
                        checked={selectedRepos.has(repo.id)}
                        onChange={() => handleRepoToggle(repo.id)}
                        className="mt-1"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-medium truncate">{repo.name}</h4>
                          {repo.private ? (
                            <Lock className="w-3 h-3 text-gray-400" />
                          ) : (
                            <Globe className="w-3 h-3 text-gray-400" />
                          )}
                        </div>
                        <p className="text-sm text-gray-500 truncate mb-2">
                          {repo.fullName}
                        </p>
                        {repo.description && (
                          <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                            {repo.description}
                          </p>
                        )}
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          {repo.language && (
                            <div className="flex items-center space-x-1">
                              <div className={`w-2 h-2 rounded-full ${getLanguageColor(repo.language)}`} />
                              <span>{repo.language}</span>
                            </div>
                          )}
                          <div className="flex items-center space-x-1">
                            <Star className="w-3 h-3" />
                            <span>{repo.stars}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <GitFork className="w-3 h-3" />
                            <span>{repo.forks}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))
      )}
    </div>
  );
}
