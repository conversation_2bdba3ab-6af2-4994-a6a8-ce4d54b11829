import { getServerSession } from "next-auth/next";
import { authOptions } from "@/src/features/auth/lib/auth-config";
import { NextResponse } from "next/server";
import { Organization, Account } from "@/src/lib/database/models";
import { withDatabase } from "@/src/lib/middleware/database";

async function getHandler(
  request: Request,
  { params }: { params: Promise<{ orgId: string }> }
) {
  console.log(`🚀 Main repositories route called`);
  try {
    const session = await getServerSession(authOptions);
    console.log(`🔐 Session check: ${session?.user ? 'authenticated' : 'not authenticated'}`);

    if (!session?.user) {
      console.log(`❌ Authentication failed`);
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    const { orgId } = await params;
    console.log(`📋 Params: orgId=${orgId}`);



    // First, find the organization to determine its provider
    console.log(`🔍 Looking for organization with orgId: ${orgId}, userId: ${session.user.id}`);
    const organization = await Organization.findOne({
      org_id: orgId,
      user_id: session.user.id
    });

    if (!organization) {
      console.log(`❌ Organization not found for orgId: ${orgId}, userId: ${session.user.id}`);
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    console.log(`✅ Found organization: ${organization.org_name} (provider: ${organization.provider})`);

    const provider = organization.provider; // 'github' or 'gitlab'
    console.log(`🔧 Using provider: ${provider}`);
    console.log(`🚨 FORCE RECOMPILE - About to search for accounts`);

    // First, let's check if there's a GitLab account with undefined userId and fix it
    const brokenGitlabAccount = await Account.findOne({ provider: 'gitlab', userId: { $exists: false } });
    if (brokenGitlabAccount) {
      console.log(`🔧 Found broken GitLab account, fixing userId...`);
      await Account.findByIdAndUpdate(brokenGitlabAccount._id, { userId: session.user.id });
      console.log(`✅ Fixed GitLab account userId`);
    }

    // Get user accounts
    console.log(`🔍 Searching for accounts with userId: ${session.user.id}`);

    // First, let's fix any broken GitLab accounts
    const brokenAccounts = await Account.find({
      provider: 'gitlab',
      $or: [
        { userId: { $exists: false } },
        { userId: null },
        { userId: undefined }
      ]
    });
    console.log(`🔧 Found ${brokenAccounts.length} broken GitLab accounts`);

    if (brokenAccounts.length > 0) {
      for (const account of brokenAccounts) {
        console.log(`🔧 Fixing account ${account._id} - setting userId to ${session.user.id}`);
        await Account.findByIdAndUpdate(account._id, { userId: session.user.id });
      }
      console.log(`✅ Fixed ${brokenAccounts.length} GitLab accounts`);
    }

    const accounts = await Account.find({
      userId: session.user.id
    });
    console.log(`👥 Found ${accounts.length} accounts for user ${session.user.id}:`, accounts.map(acc => ({ provider: acc.provider, hasToken: !!acc.access_token })));

    // Also try to find all accounts to see what's in the database
    const allAccounts = await Account.find({});
    console.log(`🗂️ Total accounts in database: ${allAccounts.length}`);
    if (allAccounts.length > 0) {
      console.log(`📄 Sample account:`, {
        userId: allAccounts[0].userId,
        provider: allAccounts[0].provider,
        hasToken: !!allAccounts[0].access_token
      });

      // Check if there's a GitLab account with undefined userId that we can fix
      const gitlabAccountWithoutUserId = allAccounts.find(acc => acc.provider === 'gitlab' && !acc.userId);
      if (gitlabAccountWithoutUserId) {
        console.log(`🔧 Found GitLab account without userId, fixing it...`);
        await Account.findByIdAndUpdate(gitlabAccountWithoutUserId._id, {
          userId: session.user.id
        });
        console.log(`✅ Fixed GitLab account userId`);

        // Re-fetch accounts after the fix
        const updatedAccounts = await Account.find({
          userId: session.user.id
        });
        console.log(`🔄 Re-fetched accounts after fix: ${updatedAccounts.length}`);
      }
    }

    const allRepositories = [];
    const connectedProviders = [];
    const errors = [];

    // Handle GitHub provider
    if (provider === 'github') {
      const githubAccount = accounts.find(acc => acc.provider === 'github');

      if (githubAccount?.access_token) {
        try {
          let apiUrl: string;
          let orgName: string | null = null;

          // Determine API URL based on organization type
          if (organization.org_type === 'personal') {
            // Fetch user's personal repositories
            apiUrl = 'https://api.github.com/user/repos?per_page=100&sort=updated&affiliation=owner';
          } else {
            // Fetch organization repositories
            // First, get the org name from the org ID
            const orgResponse = await fetch(`https://api.github.com/organizations/${orgId}`, {
              headers: {
                'Authorization': `Bearer ${githubAccount.access_token}`,
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'Platyfend-Dashboard'
              },
            });

            if (orgResponse.ok) {
              const org = await orgResponse.json();
              orgName = org.login;
              apiUrl = `https://api.github.com/orgs/${orgName}/repos?per_page=100&sort=updated`;
            } else {
              throw new Error(`Failed to fetch organization details: ${orgResponse.status}`);
            }
          }

          const response = await fetch(apiUrl, {
            headers: {
              'Authorization': `Bearer ${githubAccount.access_token}`,
              'Accept': 'application/vnd.github.v3+json',
              'User-Agent': 'Platyfend-Dashboard'
            },
          });

          if (response.ok) {
            const repos = await response.json();
            const githubRepos = repos.map((repo: any) => ({
              id: `github-${repo.id}`,
              externalId: repo.id.toString(),
              name: repo.name,
              fullName: repo.full_name,
              provider: 'github',
              isPrivate: repo.private,
              private: repo.private, // Add both for compatibility
              language: repo.language,
              stars: repo.stargazers_count || 0,
              forks: repo.forks_count || 0,
              description: repo.description || '',
              url: repo.html_url,
              defaultBranch: repo.default_branch || 'main',
              lastActivity: repo.updated_at || new Date().toISOString(),
              lastSync: new Date().toISOString(),
              addedAt: new Date().toISOString()
            }));

            allRepositories.push(...githubRepos);
            connectedProviders.push({
              provider: 'github',
              accountName: orgName || githubAccount.providerAccountId,
              repositoryCount: githubRepos.length
            });
          } else {
            const errorText = await response.text();
            errors.push({
              provider: 'github',
              error: `HTTP ${response.status}: ${response.statusText} - ${errorText}`
            });
          }
        } catch (error: any) {
          errors.push({
            provider: 'github',
            error: error.message
          });
        }
      } else {
        // GitHub account not connected
        errors.push({
          provider: 'github',
          error: 'GitHub account not connected'
        });
      }
    }

    // Handle GitLab provider
    else if (provider === 'gitlab') {
      console.log(`🔍 Handling GitLab provider for orgId: ${orgId}`);
      const gitlabAccount = accounts.find(acc => acc.provider === 'gitlab');
      console.log(`🔑 GitLab account found: ${gitlabAccount ? 'yes' : 'no'}, has token: ${gitlabAccount?.access_token ? 'yes' : 'no'}`);

      if (gitlabAccount?.access_token) {
        try {
          let apiUrl: string;
          let groupName: string | null = null;

          // Determine API URL based on organization type
          console.log(`📋 Organization type: ${organization.org_type}`);
          if (organization.org_type === 'personal') {
            // Fetch user's repositories using membership=true to get all accessible repos
            apiUrl = 'https://gitlab.com/api/v4/projects?membership=true&per_page=100&order_by=last_activity_at&sort=desc';
            console.log(`🔗 Using personal repos API: ${apiUrl}`);
          } else {
            // Fetch group projects
            // First, get the group name from the group ID
            const groupResponse = await fetch(`https://gitlab.com/api/v4/groups/${orgId}`, {
              headers: {
                'Authorization': `Bearer ${gitlabAccount.access_token}`,
                'User-Agent': 'Platyfend-Dashboard'
              },
            });

            if (groupResponse.ok) {
              const group = await groupResponse.json();
              groupName = group.name;
              apiUrl = `https://gitlab.com/api/v4/groups/${orgId}/projects?per_page=100&order_by=last_activity_at&sort=desc`;
            } else {
              throw new Error(`Failed to fetch group details: ${groupResponse.status} ${groupResponse.statusText}`);
            }
          }

          const response = await fetch(apiUrl, {
            headers: {
              'Authorization': `Bearer ${gitlabAccount.access_token}`,
              'User-Agent': 'Platyfend-Dashboard'
            },
          });
          console.log("GitLab API response:", response.status, response.statusText);

          if (response.ok) {
            const projects = await response.json();
            const gitlabRepos = projects.map((project: any) => ({
              id: `gitlab-${project.id}`,
              externalId: project.id.toString(),
              name: project.name,
              fullName: project.path_with_namespace,
              provider: 'gitlab',
              isPrivate: project.visibility === 'private',
              private: project.visibility === 'private', // Add both for compatibility
              language: project.default_branch || 'main',
              stars: project.star_count || 0,
              forks: project.forks_count || 0,
              description: project.description || '',
              url: project.web_url,
              defaultBranch: project.default_branch || 'main',
              lastActivity: project.last_activity_at || new Date().toISOString(),
              lastSync: new Date().toISOString(),
              addedAt: new Date().toISOString()
            }));

            allRepositories.push(...gitlabRepos);
            connectedProviders.push({
              provider: 'gitlab',
              accountName: groupName || gitlabAccount.providerAccountId,
              repositoryCount: gitlabRepos.length
            });
          } else {
            const errorText = await response.text();
            errors.push({
              provider: 'gitlab',
              error: `HTTP ${response.status}: ${response.statusText} - ${errorText}`
            });
          }
        } catch (error: any) {
          console.error('GitLab API error:', error);
          errors.push({
            provider: 'gitlab',
            error: error.message
          });
        }
      } else {
        // GitLab account not connected
        errors.push({
          provider: 'gitlab',
          error: 'GitLab account not connected'
        });
      }
    }
    // Handle unsupported providers
    else {
      return NextResponse.json({
        error: `Unsupported provider: ${provider}`
      }, { status: 400 });
    }

    // Check what providers are missing (simplified since we only handle one provider per request)
    const missingProviders = [];
    if (provider === 'github' && !accounts.find(acc => acc.provider === 'github')) {
      missingProviders.push('github');
    }
    if (provider === 'gitlab' && !accounts.find(acc => acc.provider === 'gitlab')) {
      missingProviders.push('gitlab');
    }

    // Sort repositories by last activity
    allRepositories.sort((a, b) =>
      new Date(b.lastActivity).getTime() - new Date(a.lastActivity).getTime()
    );

    return NextResponse.json({
      repositories: allRepositories,
      totalCount: allRepositories.length,
      connectedProviders,
      missingProviders,
      errors: errors.length > 0 ? errors : undefined,
      organizationId: orgId,
      user: {
        id: session.user.id,
        email: session.user.email,
        name: session.user.name
      }
    });

  } catch (error: any) {
    console.error("Available repositories API error:", error);
    return NextResponse.json({
      error: "Failed to fetch available repositories",
      details: error.message
    }, { status: 500 });
  }
}

// Export handler wrapped with database middleware
export const GET = withDatabase(getHandler);