import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/src/features/auth/lib/auth-config'
import { Organization, IRepository } from '@/src/lib/database/models'
import { withDatabase } from '@/src/lib/middleware/database'

interface RepositoryToAdd {
  id: string;
  externalId: string;
  name: string;
  fullName: string;
  provider: 'github' | 'gitlab';
  private: boolean;
  language?: string;
  stars: number;
  forks: number;
  description?: string;
  url?: string;
  defaultBranch?: string;
  lastActivity: string;
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ orgId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    const { orgId } = await params;
    const body = await request.json();
    const { repositories }: { repositories: RepositoryToAdd[] } = body;

    if (!repositories || !Array.isArray(repositories) || repositories.length === 0) {
      return NextResponse.json({ error: "No repositories provided" }, { status: 400 });
    }

    // Ensure database connection
    await withDatabase(async () => {
      // Find the organization
      const organization = await Organization.findOne({
        $or: [
          { _id: orgId },
          { org_id: orgId },
          { github_org_id: orgId },
          { gitlab_group_id: orgId }
        ]
      });

      if (!organization) {
        throw new Error('Organization not found');
      }

      // Check if user has access to this organization
      const hasAccess = organization.members?.some((member: any) => 
        member.user_id === session.user.id
      ) || organization.owner_id === session.user.id;

      if (!hasAccess) {
        throw new Error('Access denied to this organization');
      }

      // Get current repository IDs to avoid duplicates
      const currentRepoIds = new Set(organization.repos?.map((repo: IRepository) => repo.repo_id) || []);

      // Transform repositories to our format and filter out duplicates
      const reposToAdd: IRepository[] = repositories
        .filter(repo => !currentRepoIds.has(repo.externalId))
        .map((repo: RepositoryToAdd) => ({
          repo_id: repo.externalId,
          name: repo.name,
          full_name: repo.fullName,
          provider: repo.provider,
          private: repo.private,
          description: repo.description || '',
          language: repo.language || '',
          stars: repo.stars || 0,
          forks: repo.forks || 0,
          url: repo.url || '',
          default_branch: repo.defaultBranch || 'main',
          permissions: ['read', 'metadata'], // Default permissions
          last_sync: new Date(),
          added_at: new Date(),
          webhook_configured: false,
          status: 'active'
        }));

      if (reposToAdd.length === 0) {
        return NextResponse.json({ 
          message: "All selected repositories are already added",
          addedCount: 0,
          skippedCount: repositories.length
        });
      }

      // Add repositories to organization
      const updateResult = await Organization.updateOne(
        { _id: organization._id },
        {
          $push: { repos: { $each: reposToAdd } },
          $set: { updated_at: new Date() }
        }
      );

      if (updateResult.modifiedCount === 0) {
        throw new Error('Failed to add repositories to organization');
      }

      return NextResponse.json({
        message: `Successfully added ${reposToAdd.length} repository(ies)`,
        addedCount: reposToAdd.length,
        skippedCount: repositories.length - reposToAdd.length,
        repositories: reposToAdd.map(repo => ({
          id: repo.repo_id,
          name: repo.name,
          fullName: repo.full_name,
          provider: repo.provider
        }))
      });
    });

  } catch (error: any) {
    console.error('Error adding repositories:', error);
    
    if (error.message === 'Organization not found') {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }
    
    if (error.message === 'Access denied to this organization') {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    return NextResponse.json(
      { error: error.message || "Failed to add repositories" },
      { status: 500 }
    );
  }
}

// GET endpoint to fetch available repositories for adding
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orgId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    const { orgId } = await params;

    // This will reuse the existing logic from the main repositories route
    // but filter out repositories that are already added to the organization
    const repositoriesResponse = await fetch(
      `${request.nextUrl.origin}/api/organizations/${orgId}/repositories`,
      {
        headers: {
          'Cookie': request.headers.get('Cookie') || '',
        },
      }
    );

    if (!repositoriesResponse.ok) {
      const errorData = await repositoriesResponse.json();
      return NextResponse.json(errorData, { status: repositoriesResponse.status });
    }

    const repositoriesData = await repositoriesResponse.json();

    // Get organization to check which repositories are already added
    await withDatabase(async () => {
      const organization = await Organization.findOne({
        $or: [
          { _id: orgId },
          { org_id: orgId },
          { github_org_id: orgId },
          { gitlab_group_id: orgId }
        ]
      });

      if (organization) {
        const addedRepoIds = new Set(organization.repos?.map((repo: IRepository) => repo.repo_id) || []);
        
        // Filter out repositories that are already added
        repositoriesData.repositories = repositoriesData.repositories.filter(
          (repo: any) => !addedRepoIds.has(repo.externalId)
        );
      }
    });

    return NextResponse.json(repositoriesData);

  } catch (error: any) {
    console.error('Error fetching available repositories:', error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch available repositories" },
      { status: 500 }
    );
  }
}
